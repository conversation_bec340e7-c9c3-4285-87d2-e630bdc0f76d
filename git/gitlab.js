const axios = require('axios');
const { getValue, setValue } = require('../util/storage');
const inquirer = require('inquirer');

const gitlabUrl = 'https://gitlab.com';

async function getDockerTagsForService(fullRepoName, tag) {
    const token = await getGitlabToken();
    if (!token) {
        throw new Error('Token do GitLab não encontrado');
    }

    const projectId = getProjectIdFromRepoName(fullRepoName);
    const url = `${gitlabUrl}/api/v4/projects/${projectId}/registry/repositories`;
    const response = await axios.get(url, {
        headers: {
            'Private-Token': token
        }
    });

    const repoNameWithoutTag = fullRepoName.split(':')[0];

    const repositories = response.data;
    const repository = repositories.find(repo => repo.location === repoNameWithoutTag);
    if (!repository) {
        throw new Error(`Não foi encontrado respositórios no gitlab para a image: ${fullRepoName}`);
    }

    const tagsUrl = `${gitlabUrl}/api/v4/projects/${projectId}/registry/repositories/${repository.id}/tags`;
    const tagsResponse = await axios.get(tagsUrl, {
        headers: {
            'Private-Token': token
        }
    });

    return tagsResponse.data;
}

async function getDockerTags(projectId, registryId) {
    const url = `${gitlabUrl}/api/v4/projects/${projectId}/registry/repositories/${registryId}/tags`;
    const response = await axios.get(url);
    return response.data;
}

async function getGitlabToken() {
    if (process.env.GITLAB_TOKEN) {
        setValue('gitlab_token', process.env.GITLAB_TOKEN);
    }

    let token = getValue('gitlab_token');
    if (!token) {
        const answers = await inquirer.prompt([
            {
                type: 'password',
                name: 'token',
                message: 'Por favor, insira o token do GitLab:',
            }
        ]);
        token = answers.token;
        setValue('gitlab_token', token);
    }

    return token;
}

async function getGitlabTokenExportLogs() {
    if (process.env.GITLAB_TOKEN_EXPORT_LOGS) {
        setValue('gitlab_token_export_logs', process.env.GITLAB_TOKEN_EXPORT_LOGS);
    }

    let token = getValue('gitlab_token_export_logs');
    if (!token) {
        const answers = await inquirer.prompt([
            {
                type: 'password',
                name: 'token',
                message: 'Por favor, insira o token do GitLab:',
            }
        ]);
        token = answers.token;
        setValue('gitlab_token_export_logs', token);
    }

    return token;
}

function getProjectIdFromRepoName(repoName) {
    let serviceName = repoName;
    if (repoName.indexOf('/') !== -1) {
        serviceName = repoName.split('/')[2];
    }

    const projectId = serviceToProjectIdMap[serviceName];
    if (!projectId) {
        throw new Error(`No project found for service: ${serviceName}`);
    }
    return projectId;
}

const serviceToProjectIdMap = {
    "autenticacao-ms": 14810163,
    "discovery-ms": 16175390,
    "graduacao-ms": 15060099,
    "relatorio-full-ms": 21105750,
    "cliente-ms": 30476255,
    "bi-ms": 27522375,
    "media-ms": 32773141,
    "leads-ms": 15310679,
    "cad-aux-ms": 27761224,
    "crm-ms": 36632339,
    "adm-core-ms": 31365289,
    "personagem-ms": 16893863,
    "pessoa-ms": 35205490,
    "plano-ms": 20698475,
    "produto-ms": 22790099,
    "pactopay-ms": 35333838,
    "acesso-sistema-ms": 33814380,
    "adm-ms": 41130932,
    "midia-social-ms": 42177345,
    "marketing-ms": 41803098,
    "notificacao-ms": 14776216,
    "financeiro-ms": 39423146,
    "recurso-ms": 45076436,
    "relatorio-ms": 32921814,
    "clube-vantagens-ms": 32537267,
    "zw": 11991911,
    "treino": 12009656,
    "oamd": 11997035,
    "api": 12232585,
    "login": 11978135,
    "teste-auto": 33594913
};


async function getGitlabProjects() {
    const gitlabToken = await getGitlabToken();

    try {
        const response = await axios.get(`${gitlabUrl}/api/v4/projects`, {
            headers: {
                'Private-Token': gitlabToken
            },
            params: {
                membership: true,
                order_by: 'last_activity_at',
                sort: 'desc',
                per_page: 100
            }
        });

        return response.data.map(project => ({
            id: project.id,
            name: project.name,
            fullName: project.name_with_namespace,
            description: project.description,
            url: project.web_url,
            defaultBranch: project.default_branch
        }));
    } catch (error) {
        throw new Error(`Failed to fetch GitLab projects: ${error.message}`);
    }
};

async function getOpenMergeRequests(projectId) {
    const gitlabToken = await getGitlabToken();
    const response = await axios.get(
        `${gitlabUrl}/api/v4/projects/${projectId}/merge_requests`,
        {
            headers: { 'PRIVATE-TOKEN': gitlabToken },
            params: {
                state: 'opened'
            }
        }
    );
    return response.data;
}

async function getMRDiff(projectId, mrIid) {
    try {
        const gitlabToken = await getGitlabToken();
        const response = await axios.get(
            `${gitlabUrl}/api/v4/projects/${projectId}/merge_requests/${mrIid}/diffs`,
            { headers: { 'Private-Token': gitlabToken } }
        );
        return response.data;
    } catch (error) {
        console.error('Error fetching MR changes');
        throw new Error(error);
    }
}

async function postMRComments(projectId, mrIid, review) {
    const gitlabToken = await getGitlabToken();
    const url = `${gitlabUrl}/api/v4/projects/${encodeURIComponent(projectId)}/merge_requests/${mrIid}/notes`;
    const payload = {
        body: review,
    };

    const response = await axios.post(url, payload, {
        headers: { 'Private-Token': gitlabToken },
    });
    return response.data;
}

async function postMRLineComments(projectId, mrIid, comment, position) {
    try {
        const gitlabToken = await getGitlabToken();
        const url = `${gitlabUrl}/api/v4/projects/${encodeURIComponent(projectId)}/merge_requests/${mrIid}/discussions`;
        
        const payload = {
            body: comment,
            position: {
                base_sha: position.base_sha,
                start_sha: position.start_sha,
                head_sha: position.head_sha,
                position_type: position.position_type || 'text',
                new_path: position.new_path,
                new_line: position.new_line
            }
        };

        const response = await axios.post(url, payload, {
            headers: { 
                'Private-Token': gitlabToken,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error posting MR line comments');
        throw new Error(error);
    }
}

async function getMergeRequestData(projectId, mergeRequestIid) {
    const gitlabToken = await getGitlabToken();
    const response = await axios.get(
        `${gitlabUrl}/api/v4/projects/${projectId}/merge_requests/${mergeRequestIid}`,
        {
            headers: {
                'PRIVATE-TOKEN': gitlabToken
            }
        }
    );
    return response.data;
}

function getRepoNameFromMergeRequestUrl(gitlabMergeRequesgtUrl) {
    const pathWithMr = gitlabMergeRequesgtUrl.replace('https://gitlab.com/', '');

    const pathParts = pathWithMr.split('/-/merge_requests/')[0];

    const projectPathSegments = pathParts.split('/');

    const projectName = projectPathSegments[projectPathSegments.length - 1];

    return projectName;
}

async function getMergeRequestBranches(gitlabMergeRequestUrl) {
    const gitlabToken = await getGitlabToken();
    try {
      const urlParts = gitlabMergeRequestUrl.split('/-/merge_requests/');
      const projectPath = urlParts[0].replace('https://gitlab.com/', '');
      const mergeRequestIid = urlParts[1];
  
      const encodedProjectPath = encodeURIComponent(projectPath).replace(/%2F/g, '%2F');
  
      const apiUrl = `https://gitlab.com/api/v4/projects/${encodedProjectPath}/merge_requests/${mergeRequestIid}`;
  
      const response = await axios.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': gitlabToken,
        },
      });
  
      const { source_branch, target_branch } = response.data;
      return {
        sourceBranch: source_branch,
        targetBranch: target_branch,
        status: response.data.state,
      };
    } catch (error) {
      console.error('Erro ao obter as branches do merge request:', error.response?.data || error.message);
      throw error;
    }
}


module.exports = { getGitlabTokenExportLogs, getDockerTags, getRepoNameFromMergeRequestUrl, getMergeRequestBranches, getDockerTagsForService, getGitlabToken, getProjectIdFromRepoName, getGitlabProjects, getOpenMergeRequests, serviceToProjectIdMap, getMRDiff, postMRComments, postMRLineComments, getMergeRequestData };
