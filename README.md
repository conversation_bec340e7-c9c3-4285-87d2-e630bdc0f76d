# 🚀 Pacto Dev Tools

<div align="center">

![Pacto Dev Tools](https://via.placeholder.com/600x200/4A90E2/FFFFFF?text=🚀+Pacto+Dev+Tools)

**A ferramenta que vai fazer você se apaixonar por deploy novamente!** 💙

*Porque a vida é muito curta para ficar digitando comandos Docker gigantes* 😅

![Made with ❤️](https://img.shields.io/badge/Made%20with-❤️-red.svg)
![Powered by ☕](https://img.shields.io/badge/Powered%20by-☕-brown.svg)
![Bugs? What bugs? 🐛](https://img.shields.io/badge/Bugs%3F-What%20bugs%3F%20🐛-green.svg)

</div>

---

## 🎭 O que é isso?

Imagine se o Docker Compose e o Jira tivessem um bebê super inteligente que cresceu bebendo café e lendo documentação técnica... 🤖☕

**Pacto Dev Tools** é a ferramenta de linha de comando que vai transformar seu dia de desenvolvedor de:

```
😫 "Onde diabos está aquele comando do Docker?"
😤 "Por que esse container não sobe?"
😵 "Qual era mesmo a branch que eu estava trabalhando?"
```

Para:

```
😎 "pacto deploy" - BOOM! Tudo funcionando!
🎉 "pacto deploy log" - Logs lindos e coloridos!
🚀 "pacto code review" - IA fazendo code review pra mim!
```

## ✨ Por que você vai amar?

- 🎨 **Logs coloridos e com emojis** - Porque preto e branco é coisa do século passado
- 🧠 **Interface inteligente** - Menus interativos que fazem você se sentir no futuro
- 🤖 **Integração com IA** - Code reviews automáticos (sim, a IA trabalha pra você!)
- 📋 **Jira integrado** - Porque alguém tem que organizar essa bagunça
- 🐳 **Docker simplificado** - Adeus comandos gigantes, olá simplicidade
- ⚡ **Velocidade ninja** - Deploy em segundos, não em horas

## 🎪 O que você pode fazer?

| Ação | Comando Mágico | O que acontece |
|------|----------------|----------------|
| 🚀 Deploy rápido | `pacto deploy` | Containers sobem mais rápido que foguete da SpaceX |
| 🔍 Ver o que tá rolando | `pacto deploy review` | Lista todos os containers como um chefe |
| 🛑 Parar a festa | `pacto deploy stop` | Para tudo com elegância |
| 📝 Espiar os logs | `pacto deploy log` | Logs coloridos que até sua mãe entenderia |
| 🤖 Code review automático | `pacto code review` | IA faz o trabalho chato pra você |
| 🔍 Buscar no Jira | `pacto jira busca` | Encontra suas issues mais rápido que Google |

<div align="center">

![Demo GIF](https://via.placeholder.com/500x300/FF6B6B/FFFFFF?text=🎬+Demo+em+Ação!)

*Seus colegas vão ficar com inveja dos seus logs coloridos* 😏

</div>

---

## 🎯 Antes de Começar a Diversão

<div align="center">

![Checklist](https://via.placeholder.com/400x200/28A745/FFFFFF?text=📋+Checklist+do+Dev)

*"Preparação é a chave do sucesso... e de não quebrar nada em produção"* 🔧

</div>

### 📦 O que você precisa ter (não se preocupe, é moleza!)

- 🟢 **Node.js** - O motor que faz tudo funcionar ([baixar aqui](https://nodejs.org/))
  - *Se você não tem Node.js, é como tentar fazer café sem café... não rola* ☕
- 🐳 **Docker** - Porque containers são o futuro (e o presente também)
  - *Docker é tipo Lego para adultos, mas que realmente funciona* 🧱
- 🔑 **Token do GitLab** - Sua chave para o reino dos containers

### 🔐 Configuração Rápida do Docker

```bash
docker login registry.gitlab.com -u <seu-usuario> -p <seu-personal-access-token>
```

> 💡 **Dica de ouro**: Gere seu token [aqui](https://gitlab.com/-/profile/personal_access_tokens) - é mais fácil que pedir pizza!

---

## 🎪 Hora da Instalação!

<div align="center">

![Installation](https://via.placeholder.com/500x250/FF9500/FFFFFF?text=🛠️+Instalação+Mágica)

*"Escolha sua aventura: NPM ou Docker?"* 🎮

</div>

### 🥇 Opção 1: Via NPM (Para os Corajosos)

<div align="center">

![NPM](https://img.shields.io/badge/NPM-CB3837?style=for-the-badge&logo=npm&logoColor=white)

*"A instalação clássica - testada e aprovada por desenvolvedores do mundo todo!"* 🌍

</div>

Instale o dev-tools como um pacote npm privado. É mais fácil que montar um móvel da IKEA! 📦

#### Pré-requisitos

- Node.js instalado em sua máquina ([baixar aqui](https://nodejs.org/))
- Token de acesso pessoal do GitLab com escopo `read_api` e `read_repository`

#### Configuração da Autenticação

Antes de instalar o pacote, você precisa configurar a autenticação para acessar o GitLab Package Registry.

##### 1. Gerar Token de Acesso Pessoal

1. Acesse [GitLab Personal Access Tokens](https://gitlab.com/-/profile/personal_access_tokens)
2. Crie um novo token com os seguintes escopos:
   - `read_api`
   - `read_repository`
3. Copie o token gerado (você não poderá vê-lo novamente)

##### 2. Configurar Autenticação no NPM

**📋 Configuração para Windows:**

Se você estiver usando Windows com PowerShell, primeiro configure a política de execução:

```powershell
Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
```

> 💡 **Nota**: Este comando permite a execução de scripts do PowerShell necessários para o npm funcionar corretamente no Windows.

Você pode configurar a autenticação de duas formas:

**Opção A: Arquivo .npmrc (Recomendado)**

Crie ou edite o arquivo `.npmrc` na raiz do seu projeto ou no diretório home do usuário:

```bash
# Para projeto específico
echo "@plataformazw:registry=https://gitlab.com/api/v4/packages/npm/" >> .npmrc
echo "//gitlab.com/api/v4/packages/npm/:_authToken=SEU_TOKEN_AQUI" >> .npmrc
```

```bash
# Para configuração global
npm config set @plataformazw:registry https://gitlab.com/api/v4/packages/npm/
npm config set //gitlab.com/api/v4/packages/npm/:_authToken SEU_TOKEN_AQUI
```

**Opção B: Variável de Ambiente**

```bash
export NPM_TOKEN=SEU_TOKEN_AQUI
echo "@plataformazw:registry=https://gitlab.com/api/v4/packages/npm/" >> .npmrc
echo "//gitlab.com/api/v4/packages/npm/:_authToken=\${NPM_TOKEN}" >> .npmrc
```

#### Instalação do Pacote

Após configurar a autenticação, instale o pacote:

##### Instalação Global

```bash
npm install -g @plataformazw/dev-tools
```

##### Instalação Local (em um projeto)

```bash
npm install @plataformazw/dev-tools
```

#### Verificação da Instalação

Após a instalação, verifique se o comando `pacto` está disponível:

```bash
pacto --help
```

Se a instalação foi bem-sucedida, você verá a saída com os comandos disponíveis.

#### Solução de Problemas

**Erro de Autenticação (401 Unauthorized)**
- Verifique se o token foi configurado corretamente
- Confirme se o token tem os escopos necessários (`read_api` e `read_repository`)
- Certifique-se de que o token não expirou

**Erro de Registro não encontrado (404 Not Found)**
- Verifique se a configuração do registry está correta no `.npmrc`
- Confirme se você tem acesso ao projeto no GitLab

**Comando `pacto` não encontrado após instalação global**
- Verifique se o diretório de binários globais do npm está no seu PATH
- Execute `npm config get prefix` para ver o diretório de instalação global
- Adicione `{prefix}/bin` ao seu PATH se necessário

### Opção 2: Instalação Local (Desenvolvimento)

Faça o clone do repositório

```
<NAME_EMAIL>:Plataformazw/dev-tools.git
```

Acesse a pasta do projeto

```
cd dev-tools
```

Instale as dependências

```
npm i
```

Instale a biblioteca dev-tools de linha de comando

```
npm link
```

---

## 🎛️ Configuração Inicial (A Parte Divertida!)

<div align="center">

![Configuration](https://via.placeholder.com/450x200/6F42C1/FFFFFF?text=⚙️+Setup+Time!)

*"Configurar uma vez, ser feliz para sempre!"* 🎯

</div>

### 📋 Configuração do Jira (Altamente Recomendado!)

<div align="center">

![Jira](https://img.shields.io/badge/Jira-0052CC?style=for-the-badge&logo=jira&logoColor=white)

*"Transforme o Jira de vilão em melhor amigo!"* 🦸‍♂️

</div>

Antes de usar as funcionalidades mágicas de integração com Jira (que vão fazer você parecer um ninja do desenvolvimento), vamos configurar tudo direitinho:

#### 1. Configurar Credenciais do Jira
```bash
pacto jira credenciais
# ou
pacto j c
```

Você precisará fornecer:
- **Email**: Seu email do Jira da Pacto
- **Token**: Token de API do Jira ([Como gerar](https://id.atlassian.com/manage-profile/security/api-tokens))

#### 2. Configurar Filtros de Busca
```bash
pacto jira config-busca
# ou
pacto j cb
```

Essa configuração permite definir:
- **Filtros de busca**: Issues onde você é reporter, assignee ou watcher
- **Exclusão de finalizadas**: Opção para excluir atividades com status "Done"
- **Ordenação**: Como ordenar os resultados (ex: "ORDER BY updated DESC")

**Exemplo de configuração útil:**
- ✅ Reporter = currentUser() (suas issues criadas)
- ✅ Assignee = currentUser() (suas issues atribuídas)
- ✅ Excluir atividades finalizadas
- ✅ Ordenar por "ORDER BY updated DESC"

Após essa configuração, você poderá:
- 🚀 **Deploy automatizado**: `pacto deploy` → "Buscar no Jira - Automatizado"
- 📝 **Code review integrado**: `pacto code review` → "Buscar por issue do Jira"
- 🔍 **Busca rápida**: `pacto jira busca` para encontrar suas atividades

---

## 🎮 Arsenal de Comandos Épicos

<div align="center">

![Commands](https://via.placeholder.com/550x250/E74C3C/FFFFFF?text=⚔️+Comandos+de+Guerra!)

*"Com grandes poderes vêm grandes... comandos de terminal!"* 🕷️

</div>

### 🎯 Seu Menu de Superpoderes

Execute `pacto --help` e prepare-se para a magia:

```bash
pacto --help  # O comando que revela todos os segredos! 🔮
```

### 🎯 Comandos Principais

| Comando | Atalho | Descrição |
|---------|--------|-----------|
| `pacto deploy` | `p d` | 🚀 Deploy e gerenciamento de containers Docker |
| `pacto network` | `p n` | 🌐 Configuração de rede para servidores Pacto |
| `pacto test` | `p t` | 🧪 Execução de pipelines de teste |
| `pacto jira` | `p j` | 📋 Integração com Jira |
| `pacto code` | `p c` | 💻 Code review com IA |
| `pacto database` | `p db` | 🗄️ Operações de banco de dados |
| `pacto feature` | `p f` | `pacto f` | ✨ Gerenciamento mágico de features das empresas |
| `pacto release` | `p r` | 📦 Informações de releases |

### 🚀 Exemplos de Uso Rápido

```bash
# Deploy interativo com interface amigável
pacto deploy

# Deploy de uma stack específica
pacto deploy --stack "Api"

# Deploy de serviços específicos
pacto deploy --services "postgres,memcached"

# Ver containers em execução
pacto deploy review

# Parar containers
pacto deploy stop

# Ver logs dos containers
pacto deploy log

# Code review automatizado
pacto code review

# Buscar issues no Jira
pacto jira busca

# Gerenciar features das empresas
pacto feature
```

### 💡 Dicas de Mestre Jedi

<div align="center">

![Tips](https://via.placeholder.com/400x150/FFC107/000000?text=💡+Dicas+Secretas!)

</div>

- 🎯 **Atalhos Ninja**: Todos os comandos têm atalhos. Ex: `pacto d` = `pacto deploy` (porque preguiça é virtude!)
- 🆘 **Help Mágico**: Use `--help` em qualquer comando - é como ter um manual que realmente funciona!
- 🎮 **Interface Interativa**: Menus que fazem você se sentir em um RPG (mas sem dragões)
- 🌈 **Logs Coloridos**: Porque preto e branco é coisa do século passado
- ☕ **Dica Bônus**: Funciona melhor com café (não testamos sem café, não recomendamos)

### 🎁 Easter Eggs Secretos

- Digite `pacto` sem argumentos e veja a mágica acontecer ✨
- Use `pacto deploy --help` para descobrir opções secretas 🕵️
- Os logs têm emojis escondidos - colete todos! 🏆

---

## 📚 Manual do Usuário Épico

<div align="center">

![Documentation](https://via.placeholder.com/500x200/17A2B8/FFFFFF?text=📖+Manual+Épico!)

*"A documentação que você realmente vai querer ler!"* 📚

*"Cada comando é uma aventura, cada opção é uma possibilidade!"* ✨

</div>

### 🎭 Guia Completo dos Superpoderes

Prepare-se para descobrir todos os segredos e truques que vão fazer você se sentir como um mago do desenvolvimento! 🧙‍♂️

### 🚀 deploy (d)

**Comando principal para deploy e gerenciamento de containers Docker**

Realiza deploy de aplicações Pacto em ambiente de desenvolvimento usando Docker Compose. Agora com interface simplificada e logs coloridos!

#### 🎯 Uso Principal

```bash
# Deploy interativo - escolha stacks ou serviços específicos
pacto deploy

# Deploy de stack predefinida
pacto deploy --stack "Api"

# Deploy de serviços específicos
pacto deploy --services "postgres,memcached,zw"

# Deploy com opções avançadas
pacto deploy --stack "Api" --reset-branches --update-services

# Deploy com branches específicas
pacto deploy --branches "zw=feature/nova-funcionalidade,api=hotfix/bug-123"
```

#### ⚙️ Opções Disponíveis

| Opção | Descrição | Exemplo |
|-------|-----------|---------|
| `--stack <stack>` | Nome da stack predefinida | `--stack "Api"` |
| `--services <services>` | Lista de serviços (separados por vírgula) | `--services "postgres,redis"` |
| `--reset-branches [boolean]` | Resetar branches para master | `--reset-branches` |
| `--update-services [boolean]` | Atualizar serviços antes do deploy | `--update-services` |
| `--branches <branches>` | Branches específicas por serviço | `--branches "api=develop,web=feature/123"` |
| `--disable-ports <ports>` | Desabilitar portas específicas | `--disable-ports "11211,15672"` |

#### 📋 Subcomandos

| Comando | Atalho | Descrição |
|---------|--------|-----------|
| `pacto deploy review` | `pacto d review` | 📊 Revisa containers em execução |
| `pacto deploy stop` | `pacto d stop` | ⏹️ Para containers selecionados ou todos |
| `pacto deploy restart` | `pacto d restart` | 🔄 Reinicia containers |
| `pacto deploy remove` | `pacto d remove` | 🗑️ Remove todos os containers |
| `pacto deploy log` | `pacto d log` | 📝 Visualiza logs dos containers |
| `pacto deploy branch` | `pacto d branch` | 🌿 Altera branches dos containers |
| `pacto deploy update` | `pacto d update` | ⬆️ Atualiza branches dos containers |
| `pacto deploy reset-branchs` | `pacto d reset-branchs` | 🔄 Reseta branches para master |
| `pacto deploy open-url` | `pacto d open-url` | 🌐 Abre URLs dos serviços |

### 🌐 network (n)

**Configuração de rede para conectar em servidores da Pacto**

#### Subcomandos:

- **host-docker-internal (hd)**: Configura o host host.docker.internal na sua máquina
  ```bash
  pacto network host-docker-internal
  pacto n hd
  ```

- **hosts-pipe (hp)**: Configura a conexão com servidores de teste e pipeline da Pacto
  ```bash
  pacto network hosts-pipe
  pacto n hp
  ```
  **Opções:**
  - `-d, --domains <domains>`: Domínios para configurar (separados por vírgula)
  - `-i, --ip <ip>`: IP para configurar

- **hosts-pipe-add (hpa)**: Adiciona os hosts de teste e pipeline da Pacto ao arquivo de hosts
  ```bash
  pacto network hosts-pipe-add
  pacto n hpa
  ```
  **Opções:**
  - `-d, --domains <domains>`: Domínios para configurar (separados por vírgula)
  - `-i, --ip <ip>`: IP para configurar

  ⚠️ **Nota**: Requer permissão de administrador



### 🧪 test (t)

**Execução de pipelines de teste para liberação de versão**

#### Subcomandos:

- **run-pipeline (r)**: Executa a pipeline de teste
  ```bash
  pacto test run-pipeline
  pacto t r
  ```

- **connect (c)**: Conecta a um ambiente criado na pipeline de testes
  ```bash
  pacto test connect
  pacto t c
  ```

- **wait (w)**: Aguarda a execução dos serviços de teste finalizar
  ```bash
  pacto test wait
  pacto t w
  ```
  **Opções:**
  - `-s, --stack_number <stack_number>`: Número do stack de teste
  - `-t, --timeout <timeout>`: Timeout para finalização dos testes
  - `-tnss, --timeout_no_search_service <timeout_no_search_service>`: Timeout para finalização dos testes
  - `-i, --interval <interval>`: Intervalo de verificação dos testes
  - `-ngc, --notificate_google_chat <notificate_google_chat>`: Notificar no Google Chat

### 📋 jira (j)

**Configuração e integração com o Jira**

Permite integração completa com o Jira para busca de issues, configuração de filtros e automação de workflows.

#### Subcomandos:

- **credenciais (c)**: Configura as credenciais do Jira
  ```bash
  pacto jira credenciais
  pacto j c
  ```

- **ajuda (a)**: Exibe o link para obter o token do Jira
  ```bash
  pacto jira ajuda
  pacto j a
  ```

- **config-busca (cb)**: Configura os filtros de busca do Jira
  ```bash
  pacto jira config-busca
  pacto j cb
  ```

- **busca (b)**: Busca atividades no Jira
  ```bash
  pacto jira busca
  pacto j b
  ```

### 📦 release (r)

**Consultas de informações das releases dos sistemas Pacto**

#### Subcomandos:

- **release-tags (t)**: Consulta as tags de Docker de um sistema Pacto
  ```bash
  pacto release release-tags
  pacto r t
  ```
  Consulta as tags a partir do README do projeto de teste onde são documentadas as tags de cada versão.

### 🗄️ database (db)

**Comandos relacionados ao banco de dados dos sistemas Pacto**

#### Subcomandos:

- **restore (r)**: Restaura o banco de dados de uma empresa
  ```bash
  pacto database restore [chaveOuNomeEmpresa]
  pacto db r [chaveOuNomeEmpresa]
  ```

### ✨ feature (f)

**Gerenciamento mágico de features das empresas**

*"Porque habilitar e desabilitar features deveria ser tão fácil quanto pedir pizza!"* 🍕

Permite habilitar ou desabilitar features específicas (Conversas.ai e ZW-BOOT) para empresas de forma interativa e divertida. É como ter um controle remoto para as funcionalidades do sistema! 🎮

#### 🎯 Como Funciona

```bash
# O comando mágico que faz tudo acontecer
pacto feature
pacto f
```

#### 🚀 Fluxo Épico do Comando

1. **🎭 Escolha sua feature favorita**:
   - 🤖 **Conversas.ai** - IA que conversa melhor que você no WhatsApp
   - 🚀 **ZW-BOOT** - Sistema que inicializa mais rápido que café expresso

2. **🏢 Selecione a empresa**:
   - Lista todas as empresas do banco de dados
   - Interface com busca inteligente (porque ninguém tem tempo para scrollar infinitamente)
   - Mostra nome da empresa + chave para não confundir

3. **⚡ Escolha sua ação**:
   - ✅ **Habilitar** - Libera o poder da feature
   - ❌ **Desabilitar** - Remove a feature (mas com carinho)

4. **🎪 Mágica acontece**:
   - 💾 Atualiza a coluna `modulos` no banco OAMD
   - 🔄 Faz reload do service discovery (porque cache é coisa séria)
   - 🌐 Abre automaticamente a URL de login
   - 📢 Lembra você de fazer login novamente (porque segurança é importante!)

#### 🎨 Features Disponíveis

| Feature | Código | Descrição | Emoji |
|---------|--------|-----------|-------|
| **Conversas.ai** | `IA` | Inteligência artificial para conversas | 🤖 |
| **ZW-BOOT** | `ZWB` | Sistema de inicialização rápida | 🚀 |

#### 💡 Dicas de Mestre

- 🎯 **Busca Inteligente**: Digite parte do nome da empresa para filtrar rapidamente
- 🔄 **Reload Automático**: O service discovery é recarregado automaticamente
- 🌐 **Login Automático**: A URL de login abre sozinha (porque preguiça é virtude!)
- ⚠️ **Aviso Amigável**: Sempre lembra de fazer login novamente para carregar as configurações

#### 🛡️ Pré-requisitos

- ✅ Container PostgreSQL rodando (`pacto deploy --services postgres`)
- ✅ Container ZW rodando (para buscar a URL de login)
- ✅ Service Discovery rodando (`pacto deploy --services discovery-ms`)

#### 🎪 Exemplo Prático

```bash
# Execute o comando
pacto feature

# Selecione: Conversas.ai
# Selecione: ACME Corp - acme123
# Selecione: Habilitar

# 🎉 Resultado:
# ✅ Conversas.ai habilitada com sucesso!
# 🔄 Service discovery recarregado
# 🌐 URL de login aberta automaticamente
# ⚠️ Lembrete para fazer login novamente
```

#### 🔧 O que Acontece nos Bastidores

1. **Consulta o banco**: Busca empresas na tabela `empresa` do OAMD
2. **Atualiza módulos**: Adiciona/remove códigos na coluna `modulos` (IA, ZWB)
3. **Reload discovery**: Chama `GET /reload` no service discovery
4. **Abre login**: Usa `URL_NOVO_LOGIN` do container ZW + `/pt/auth`

*"É como ter superpoderes, mas para gerenciar features!"* 🦸‍♂️

### 💻 code (c)

**Code review automatizado com Inteligência Artificial**

#### Subcomandos:

- **review (r)**: Faz um code review de um MR utilizando IA
  ```bash
  pacto code review
  pacto c r
  ```

#### 🎯 Funcionalidades do Code Review

**Duas formas de encontrar merge requests:**

1. **🔍 Buscar por projeto GitLab**:
   - Lista todos os projetos GitLab disponíveis
   - Permite selecionar um projeto
   - Lista os merge requests abertos do projeto
   - Permite selecionar um MR para review

2. **📋 Buscar por issue do Jira** (Recomendado):
   - Busca issues no Jira usando os filtros configurados
   - Permite selecionar uma issue
   - Busca automaticamente os weblinks da issue
   - Filtra apenas os links que são merge requests do GitLab
   - **Opções para múltiplos MRs**:
     - ✅ **Revisar TODOS** os merge requests da issue automaticamente
     - 🎯 Escolher um merge request específico para revisar

#### ⚙️ Pré-requisitos

- ✅ Credenciais do Jira configuradas (`pacto jira credenciais`)
- ✅ Filtros de busca configurados (`pacto jira config-busca`)
- ✅ Issues do Jira devem ter weblinks para merge requests do GitLab
- ✅ Token do GitLab configurado

#### 🚀 Fluxo do Comando

1. **Escolha o método de busca** (GitLab ou Jira)
2. **Selecione o(s) merge request(s)**
3. **Escolha o tipo de review** (inline ou geral)
4. **IA analisa o código** e gera o review
5. **Adicione observações** (opcional para refinar o review)
6. **Envie o review** como comentário no GitLab

#### 🔄 Review de Múltiplos MRs

- ⚡ Processa cada MR sequencialmente
- 🎨 Aplica o mesmo tipo de review para todos
- 📊 Exibe progresso detalhado com emojis
- 🛡️ Continua processando mesmo se um MR falhar
- 🎯 Integração consistente com outros comandos Pacto

---

## 🐳 Modo Docker: Para os Aventureiros!

<div align="center">

![Docker Mode](https://via.placeholder.com/500x250/2496ED/FFFFFF?text=🐳+Docker+Mode+ON!)

*"Porque às vezes você só quer rodar tudo em container!"* 🚢

*"Docker: Onde a mágica acontece, um container por vez!"* ✨

</div>

### 📋 Pré-requisitos
- Docker
- Docker Compose

### 🚀 Configuração do Container

Para facilitar o uso do dev-tools sem precisar instalar todas as dependências localmente, você pode utilizar o container Docker fornecido.

#### Executando comandos via container

Você pode executar qualquer comando do dev-tools através do container:

```bash
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  registry.gitlab.com/plataformazw/dev-tools:main <comando> [argumentos]
```

#### 💡 Exemplos Práticos

**1. Listar todos os comandos disponíveis:**
```bash
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  registry.gitlab.com/plataformazw/dev-tools:main --help
```

**2. Executar deploy:**
```bash
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  registry.gitlab.com/plataformazw/dev-tools:main deploy --stack "Api"
```

**3. Executar comandos do Jira:**
```bash
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  registry.gitlab.com/plataformazw/dev-tools:main jira busca
```

### ⚡ Criando um alias para facilitar o uso

Para tornar o uso mais conveniente, adicione um alias ao seu shell:

**Para Bash:**
```bash
echo 'alias pacto-docker="docker run --rm -v /var/run/docker.sock:/var/run/docker.sock registry.gitlab.com/plataformazw/dev-tools:main"' >> ~/.bashrc
source ~/.bashrc
```

**Para Zsh:**
```bash
echo 'alias pacto-docker="docker run --rm -v /var/run/docker.sock:/var/run/docker.sock registry.gitlab.com/plataformazw/dev-tools:main"' >> ~/.zshrc
source ~/.zshrc
```

**Depois de configurar o alias:**
```bash
pacto-docker deploy --stack "Api"
pacto-docker jira busca
pacto-docker code review
```

### 📁 Volumes e Persistência

O container está configurado com os seguintes volumes:
- **Código fonte**: Montado em `/app`
- **Socket Docker**: Montado para permitir interação com o Docker do host
- **Chaves SSH**: Montadas somente para leitura para operações com Git

### ⚠️ Notas Importantes

1. **Privilégios de rede**: O container executa com privilégios de rede do host para facilitar a comunicação com serviços locais.

2. **Comandos privilegiados**: Para comandos que exigem privilégios de root (como modificar arquivos de hosts), use `--privileged`:

```bash
docker run --rm --privileged \
  -v /var/run/docker.sock:/var/run/docker.sock \
  registry.gitlab.com/plataformazw/dev-tools:main network hosts-pipe-add
```

---

## 🎉 Parabéns, Você Chegou ao Final!

<div align="center">

![Congratulations](https://via.placeholder.com/600x300/FF6B6B/FFFFFF?text=🎊+Parabéns!+🎊)

*"Você agora possui o poder de fazer deploys como um ninja!"* 🥷

</div>

### 🌟 O que você ganhou hoje:

- ✨ **Superpoderes de Deploy** - Logs coloridos que fazem inveja
- 🚀 **Velocidade Ninja** - Deploy em segundos, não em horas
- 🤖 **Assistente IA** - Code reviews automáticos (porque IA trabalha 24/7)
- 📋 **Organização Jedi** - Jira e GitLab na palma da mão
- 🐳 **Flexibilidade Total** - NPM, Docker, ou o que você preferir
- 🎨 **Interface Bonita** - Porque a vida é muito curta para terminais feios

### 🚀 Próximos Passos:

1. **Instale** a ferramenta (você já sabe como!)
2. **Configure** o Jira (vai ser útil, confia!)
3. **Faça** seu primeiro deploy (`pacto deploy`)
4. **Impressione** seus colegas com logs coloridos
5. **Relaxe** e deixe a IA fazer code review pra você

<div align="center">

### 💝 Feito com muito ❤️ e ☕ pela equipe Pacto

*"Que a força do deploy esteja com você!"* ⭐

---

**🐛 Encontrou um bug?** Não se preocupe, até o melhor código tem seus momentos!
**💡 Tem uma ideia?** Adoramos ouvir sugestões malucas!
**🤝 Quer contribuir?** Venha para o lado dev da força!

[![GitLab](https://img.shields.io/badge/GitLab-FCA326?style=for-the-badge&logo=gitlab&logoColor=white)](https://gitlab.com/Plataformazw/dev-tools)
[![Made with Love](https://img.shields.io/badge/Made%20with-❤️-red?style=for-the-badge)](https://github.com)
[![Powered by Coffee](https://img.shields.io/badge/Powered%20by-☕-brown?style=for-the-badge)](https://coffee.com)

</div>
