const fs = require("fs");
const { exec, execSync } = require("child_process");
const path = require("path");
const yaml = require("yaml");
const chalk = require("chalk");
const { logSuccess, logError, logWarning, logInfo, logDeploy } = require('../../util/log.js');
const { getDockerComposeCommand } = require("../../util/docker-compose.js");
const { getRepoNameFromMergeRequestUrl } = require("./../../git/gitlab");
const { dockerLoginGitlab } = require("../../util/docker");


const getComposePath = () => {
  const composeFilePath = path.join(__dirname, "docker-compose.yml");
  if (!fs.existsSync(composeFilePath)) {
    throw new Error("Arquivo docker-compose.yml não encontrado.");
  }
  return composeFilePath;
}

async function updateComposeServiceTags(selectedServices) {
  const composeFilePath = getComposePath();

  return new Promise((resolve, reject) => {
    const composeCommand = getDockerComposeCommand();
    const process = exec(
      `${composeCommand} -p dev-tools -f "${composeFilePath}" pull ${selectedServices}`,
    );

    process.stdout.on("data", (data) => {
      logInfo(`${data}`);
    });

    process.stderr.on("data", (data) => {
      logError(`${data}`);
    });

    process.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Process exited with code ${code}`));
      } else {
        logDeploy("🚀 Deploy realizado com sucesso!");
        logInfo(
          '🌐 Certifique-se de que o host "host.docker.internal" na sua máquina esteja apontando para o seu IP de rede local...',
        );
        logInfo("⏳ Aguarde a inicialização dos serviços...");
        resolve();
      }
    });
  });
}

async function updateComposeRemovePorts(ports){
    const composeFilePath = getComposePath();
    const composeFile = yaml.parse(fs.readFileSync(composeFilePath, 'utf8'));

    if (!ports || ports.length === 0) {
        logWarning('⚠️ Nenhuma porta especificada para remoção.');
        return;
    }
    logInfo('🔄 Removendo portas do arquivo docker-compose.yml...')
    ports.forEach((port) => {
        const service = Object.keys(composeFile.services).find((service) => {
            const portsList = composeFile.services[service].ports;
            if (portsList) {
                return portsList.some((p) => p.includes(port));
            }
            return false;
        });
        if (service) {
            const portsList = composeFile.services[service].ports;
            const newPortsList = portsList.filter((p) => !p.includes(port));
            if (newPortsList.length > 0) {
                composeFile.services[service].ports = newPortsList;
            } else {
                delete composeFile.services[service].ports;
            }
            logSuccess(`✅ Porta ${port} removida do serviço ${service}.`);
        } else {
            logError(`❌ Porta ${port} não encontrada em nenhum serviço.`);
        }
    });

    fs.writeFileSync(composeFilePath, yaml.stringify(composeFile), 'utf8');
}

/**
 * Altera as tags de serviços específicos no arquivo docker-compose.yml
 * @param {Array<{serviceName: string, tagName: string}>} serviceTagPairs - Array de objetos contendo nome do serviço e tag desejada
 */
function changeComposeMultipleServiceTags(serviceTagPairs) {
  const composeFilePath = path.join(__dirname, "docker-compose.yml");
  const composeFileContent = fs.readFileSync(composeFilePath, "utf8");
  let composeConfig = yaml.parse(composeFileContent);

  serviceTagPairs.forEach(({ serviceName, tagName }) => {
    if (composeConfig.services[serviceName]) {
      const imageName = composeConfig.services[serviceName].image.split(":")[0];
      let serviceTag = tagName;

      // Tratamento especial para o postgres
      if (imageName.includes("docker-pacto/postgres")) {
        serviceTag += "-9-4";
      }

      composeConfig.services[serviceName].image = `${imageName}:${serviceTag}`;
      logInfo(`🏷️ Serviço ${serviceName} atualizado para tag: ${serviceTag}`);
    } else {
      logWarning(`⚠️ Serviço ${serviceName} não encontrado no docker-compose.yml`);
    }
  });

  fs.writeFileSync(composeFilePath, yaml.stringify(composeConfig));
  logSuccess("📄 Arquivo docker-compose.yml atualizado com sucesso!");
}

function changeComposeServiceTags(services, tagName) {
  const composeFilePath = path.join(__dirname, "docker-compose.yml");
  const composeFileContent = fs.readFileSync(composeFilePath, "utf8");
  let composeConfig = yaml.parse(composeFileContent);

  services.forEach((service) => {
    const imageName = composeConfig.services[service].image.split(":")[0];
    let serviceTag = tagName;
    if (imageName.includes("docker-pacto/postgres")) {
      serviceTag += "-9-4";
    }
    composeConfig.services[service].image = `${imageName}:${serviceTag}`;
  });

  fs.writeFileSync(composeFilePath, yaml.stringify(composeConfig));
}

function getComposeServices() {
  const file = fs.readFileSync(
    path.join(__dirname, "docker-compose.yml"),
    "utf8",
  );
  const composeConfig = yaml.parse(file);
  return Object.keys(composeConfig.services);
}

function getComposeServiceLabels() {
  const file = fs.readFileSync(
    path.join(__dirname, "docker-compose.yml"),
    "utf8",
  );
  const composeConfig = yaml.parse(file);
  const services = Object.keys(composeConfig.services);
  const labels = services.map((service) => {
    const label = composeConfig.services[service].labels;
    if (!label) {
      return null;
    }
    const labelName = Object.keys(label)[0];
    return {
      name: service,
      label: label[labelName],
    };
  }
  );
  return labels.filter((label) => label);
}

async function getRunningServices() {
  try {
    const composeCommand = getDockerComposeCommand();
    const result = execSync(
      composeCommand + ' ps --services --filter "status=running"',
    ).toString();
    return result.split("\n").filter((service) => service);
  } catch (err) {
    logError(`💥 Erro ao obter serviços em execução: ${err}`);
    return [];
  }
}

function getComposeServiceGroups() {
  return [
    {
      name: "Adm",
      services: [
        "postgres",
        'memcached',
        "dynamodb",
        "zw",
        "treino",
        "oamd",
        "login",
        "autenticacao-ms",
        "discovery-ms",
        "adm-core-ms",
        "adm-ms",
        "login-front",
        "treino-front",
        "crm-ms",
        "financeiro-ms",
        "plano-ms",
        "pessoa-ms",
        "treino",
        "relatorio-ms",
        "vendas-online-front",
        "api"
      ],
    },
    {
      name: "Treino",
      services: [
        "postgres",
        'memcached',
        "dynamodb",
        "zw",
        "treino",
        "oamd",
        "login",
        "autenticacao-ms",
        "discovery-ms",
        "treino-front",
        "login-front",
        "relatorio-ms"
      ],
    },
    {
      name: 'Zw',
      services: ['zw', 'postgres', 'memcached'],
    },
    {
      name: "Api",
      services: [
        "adm-core-ms",
        "adm-ms",
        "api",
        "api-gateway",
        "api-doc",
        "autenticacao-ms",
        "crm-ms",
        "discovery-ms",
        "financeiro-ms",
        "memcached",
        "postgres",
        "treino",
        "zw",
      ],
    },
    {
      name: "Conversas.ai",
      services: [
        'conversas-ai-api',
        'conversas-ai-worker',
        'conversas-ai-scheduler',
        'conversas-ai-docs-worker',
        'conversas-link-frontend',
        'conversas-link-backend',
        'ngrok',
        'redis',
        'redisinsight',
        'jaeger',
        'qdrant'
      ],
    },
        {
      name: "Conversas Link",
      services: [
        'conversas-link-frontend',
        'conversas-link-backend'
      ],
    },
    {
      name: "Inteligência Artificial",
      services: [
        "postgres",
        'memcached',
        "dynamodb",
        'memcached',
        "zw",
        "treino",
        "oamd",
        "login",
        "autenticacao-ms",
        "discovery-ms",
        "adm-core-ms",
        "adm-ms",
        "login-front",
        "treino-front",
        "crm-ms",
        "financeiro-ms",
        "plano-ms",
        "pessoa-ms",
        "treino",
        'conversas-ai-api',
        'conversas-ai-worker',
        'conversas-ai-scheduler',
        'conversas-ai-docs-worker',
        'jaeger',
        'ngrok',
        'redis',
        'redisinsight',
        'qdrant'
      ],
    },
  ];
}

function getComposeServiceGoupNames() {
  return getComposeServiceGroups().map((group) => group.name);
}

function getComposeServicesGroupByName(name) {
  return name === "Todos"
    ? null
    : getComposeServiceGroups().find((group) => group.name === name).services;
}

function getServiceNameByServiceLabels(projectName) {
  const composeServiceLabels = getComposeServiceLabels();
  const servicesNames = composeServiceLabels.filter(
    (service) => service.label === `projectName=${projectName}`
  );

  if (!servicesNames || servicesNames.length === 0) {
    return null;
  }

  const names = servicesNames.map((service) => {
    const serviceName = service.name;
    return serviceName;
  });

  return names;
}

function getServiceNameByGitlabMergeRequestUrl(url) {
  const projectName = getRepoNameFromMergeRequestUrl(url);
  let servicesNames = [];

  if(projectName === 'api'){
    servicesNames.push(projectName);
  }else{
    const composeServiceNames = getComposeServices();
    servicesNames = composeServiceNames.filter((service) =>
      service.trim().toLowerCase() === projectName.trim().toLowerCase(),
    );
  }

  const servicesNamesByLabel = getServiceNameByServiceLabels(projectName);
  if (servicesNamesByLabel && servicesNamesByLabel.length > 0) {
    servicesNames.push(servicesNamesByLabel);
  }

  if (!servicesNames || servicesNames.length === 0) {
    throw new Error(
      `Nenhum serviço encontrado para o projeto: ${projectName} no docker-compose.yml`,
    );
  }

  return servicesNames;
}


function getPredefinedVersionBranches(versionOption) {
    const services = ['zw', 'treino', 'treino-front'];
    let branchUpdates = [];

    if (versionOption === 'Release 5%') {
        services.forEach(service => {
            branchUpdates.push({
                sourceBranch: 'release/rc',
                tagName: 'release-rc',
                serviceName: service
            });
        });
    } else if (versionOption === 'Master') {
        services.forEach(service => {
            branchUpdates.push({
                sourceBranch: 'master',
                tagName: 'master',
                serviceName: service
            });
        });
    } else if (versionOption === 'Estável (Latest)') {
        services.forEach(service => {
            branchUpdates.push({
                sourceBranch: 'latest',
                tagName: 'latest',
                serviceName: service
            });
        });
    } else if (versionOption === 'Develop') {
        services.forEach(service => {
            branchUpdates.push({
                sourceBranch: 'develop',
                tagName: 'develop',
                serviceName: service
            });
        });
    }

    return branchUpdates;
}


function getPredefinedVersionServices() {
    return ['zw', 'treino', 'treino-front'];
}

// Função para resetar todas as tags dos serviços para as tags padrão definidas nas labels
function resetServiceTagsToDefault() {
  try {
    logInfo('🔄 Resetando tags dos serviços para as tags padrão...');

    const composeFilePath = getComposePath();
    const file = fs.readFileSync(composeFilePath, "utf8");
    const composeConfig = yaml.parse(file);

    let servicesUpdated = 0;

    // Iterar por todos os serviços
    Object.keys(composeConfig.services).forEach(serviceName => {
      const service = composeConfig.services[serviceName];

      // Verificar se o serviço tem labels e defaultTag
      if (service.labels) {
        let defaultTag = null;

        // Procurar pela label defaultTag
        service.labels.forEach(label => {
          if (typeof label === 'string' && label.startsWith('defaultTag=')) {
            defaultTag = label.split('=')[1];
          }
        });

        // Se encontrou defaultTag, atualizar a imagem
        if (defaultTag && service.image) {
          const imageParts = service.image.split(':');
          if (imageParts.length >= 2) {
            const imageBase = imageParts.slice(0, -1).join(':');
            const newImage = `${imageBase}:${defaultTag}`;

            // Só atualizar se a tag for diferente
            if (service.image !== newImage) {
              composeConfig.services[serviceName].image = newImage;
              logInfo(`🏷️ Serviço ${serviceName} resetado para tag padrão: ${defaultTag}`);
              servicesUpdated++;
            }
          }
        }
      }
    });

    // Salvar o arquivo apenas se houve mudanças
    if (servicesUpdated > 0) {
      fs.writeFileSync(composeFilePath, yaml.stringify(composeConfig));
      logSuccess(`✅ ${servicesUpdated} serviços foram resetados para suas tags padrão!`);
    } else {
      logInfo('ℹ️ Todos os serviços já estão com suas tags padrão.');
    }

    return servicesUpdated;

  } catch (error) {
    logError('💥 Erro ao resetar tags dos serviços:', error.message);
    throw error;
  }
}

module.exports = {
  getComposeServices,
  getRunningServices,
  getComposeServiceGroups,
  getComposeServiceGoupNames,
  getComposeServicesGroupByName,
  changeComposeServiceTags,
  updateComposeServiceTags,
  getServiceNameByGitlabMergeRequestUrl,
  changeComposeMultipleServiceTags,
  getPredefinedVersionBranches,
  getPredefinedVersionServices,
  updateComposeRemovePorts,
  resetServiceTagsToDefault
};
